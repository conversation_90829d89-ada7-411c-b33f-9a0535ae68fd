"""
Configuration management for AI CLI
"""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class Config:
    """Configuration class for AI CLI application"""

    api_key: str = "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"
    model: str = "qwen/qwen-2.5-coder-32b-instruct:free"
    max_tokens: int = 2048
    temperature: float = 0.7
    base_url: str = "https://openrouter.ai/api/v1"

    def __post_init__(self):
        """Initialize configuration after object creation"""
        # Use pre-configured API key - no user setup required
        pass

    @property
    def headers(self) -> dict:
        """Get headers for API requests"""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/ai-cli/ai-cli",
            "X-Title": "AI CLI"
        }