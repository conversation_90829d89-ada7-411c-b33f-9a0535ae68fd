"""
Interactive chat interface for AI CLI
"""

import sys
import re
from typing import List
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.markdown import Markdown
from rich.prompt import Prompt
from rich.live import Live
from rich.spinner import Spinner
from prompt_toolkit import prompt
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory
from prompt_toolkit.completion import WordCompleter

from .config import Config
from .api_client import OpenRouterClient, Message, APIError
from .utils import extract_code_blocks, format_code_block, count_tokens, format_token_count


class ChatInterface:
    """Interactive chat interface for the AI CLI"""

    def __init__(self, config: Config):
        self.config = config
        self.console = Console()
        self.client = OpenRouterClient(config)
        self.conversation_history: List[Message] = []
        self.history = InMemoryHistory()
        self.offline_mode = False

        # Setup command completion
        self.completer = WordCompleter([
            '/help', '/clear', '/history', '/exit', '/quit'
        ])

    def start(self):
        """Start the interactive chat session"""
        self.console.print("[green]🤖 Hello! I'm your AI assistant.[/green]")
        self.console.print("[cyan]I'm here to help with coding, questions, and conversations.[/cyan]")
        self.console.print("[yellow]Just start chatting naturally! Type '/help' if you need assistance.[/yellow]")
        self.console.print()

        # Test API connection silently
        if not self._test_connection_silently():
            return

        # Main chat loop
        while True:
            try:
                user_input = self._get_user_input()

                if not user_input.strip():
                    continue

                # Handle commands
                if user_input.startswith('/'):
                    if self._handle_command(user_input):
                        continue
                    else:
                        break

                # Process user message
                self._process_user_message(user_input)

            except KeyboardInterrupt:
                self.console.print("\n[cyan]Thanks for chatting! Have a great day! 👋[/cyan]")
                break
            except Exception as e:
                self.console.print(f"[red]Error: {e}[/red]")

    def _test_connection_silently(self) -> bool:
        """Test the API connection silently"""
        try:
            success, message = self.client.test_connection()
            if success:
                return True
            else:
                # Silently switch to offline mode if connection fails
                self.offline_mode = True
                self.console.print("[yellow]Note: AI connection unavailable, using local responses.[/yellow]")
                self.console.print()
                return True
        except:
            # Fallback to offline mode
            self.offline_mode = True
            self.console.print("[yellow]Note: AI connection unavailable, using local responses.[/yellow]")
            self.console.print()
            return True

    def _get_user_input(self) -> str:
        """Get user input with history and completion"""
        try:
            return prompt(
                "You: ",
                history=self.history,
                auto_suggest=AutoSuggestFromHistory(),
                completer=self.completer,
                complete_style="column"
            )
        except (EOFError, KeyboardInterrupt):
            raise KeyboardInterrupt

    def _handle_command(self, command: str) -> bool:
        """
        Handle chat commands

        Returns:
            True to continue chat, False to exit
        """
        command = command.lower().strip()

        if command in ['/exit', '/quit']:
            self.console.print("[cyan]Thanks for chatting! Take care! 👋[/cyan]")
            return False

        elif command == '/help':
            self._show_help()

        elif command == '/clear':
            self.conversation_history.clear()
            self.console.clear()
            self.console.print("[green]Fresh start! Our conversation history has been cleared.[/green]")

        elif command == '/history':
            self._show_history()

        else:
            self.console.print(f"[yellow]I don't recognize that command: {command}[/yellow]")
            self.console.print("[cyan]Type '/help' to see what I can do![/cyan]")

        return True

    def _show_help(self):
        """Show help information"""
        help_text = """
Available Commands:
• /help     - Show this help message
• /clear    - Start fresh (clear our conversation)
• /history  - Review our conversation history
• /exit     - End our chat session
• /quit     - End our chat session

Tips:
• Just chat naturally - I'm here to help!
• Ask me anything about coding, questions, or general topics
• Use Ctrl+C to exit anytime
• Multi-line input is supported
        """
        self.console.print(Panel(help_text, title="Help", border_style="blue"))

    def _show_history(self):
        """Show conversation history"""
        if not self.conversation_history:
            self.console.print("[yellow]We haven't started chatting yet! Say hello to begin our conversation.[/yellow]")
            return

        self.console.print(Panel("Our Conversation So Far", border_style="cyan"))
        for i, message in enumerate(self.conversation_history, 1):
            role_color = "green" if message.role == "user" else "blue"
            role_name = "You" if message.role == "user" else "AI"
            self.console.print(f"[{role_color}]{i}. {role_name}:[/{role_color}]")
            self.console.print(f"   {message.content[:100]}{'...' if len(message.content) > 100 else ''}")
            self.console.print()

    def _process_user_message(self, user_input: str):
        """Process user message and get AI response"""
        # Add user message to history
        user_message = Message(role="user", content=user_input)
        self.conversation_history.append(user_message)

        if self.offline_mode:
            # Offline mode - provide helpful response
            self._handle_offline_response(user_input)
            return

        # Prepare messages for API (include conversation context)
        messages = self.conversation_history.copy()

        try:
            # Show thinking indicator
            with Live(Spinner("dots", text="Thinking..."), console=self.console):
                response = self.client.chat_completion(messages)

            # Extract AI response
            if 'choices' in response and len(response['choices']) > 0:
                ai_content = response['choices'][0]['message']['content']
                ai_message = Message(role="assistant", content=ai_content)
                self.conversation_history.append(ai_message)

                # Display AI response with formatting
                self._display_ai_response(ai_content)
            else:
                self.console.print("[red]Error: No response from AI[/red]")

        except APIError as e:
            self.console.print("[yellow]Switching to demo mode for now...[/yellow]")
            self.offline_mode = True
            self._handle_offline_response(user_input)
        except Exception as e:
            self.console.print("[yellow]Switching to demo mode for now...[/yellow]")
            self.offline_mode = True
            self._handle_offline_response(user_input)

    def _display_ai_response(self, content: str):
        """Display AI response with proper formatting"""
        self.console.print()

        # Count tokens in the response
        token_count = count_tokens(content, self.config.model)
        token_display = format_token_count(token_count)

        self.console.print(f"[blue]AI:[/blue] [dim]({token_display})[/dim]")

        # Extract and format code blocks separately
        code_blocks = extract_code_blocks(content)

        if code_blocks:
            # If there are code blocks, process them specially
            remaining_content = content

            for code, language in code_blocks:
                # Remove the code block from content for markdown rendering
                code_pattern = f"```{language or ''}\n{re.escape(code)}\n```"
                remaining_content = re.sub(code_pattern, "[CODE_BLOCK_PLACEHOLDER]", remaining_content, count=1)

            # Render remaining content as markdown
            try:
                if remaining_content.strip() and remaining_content.strip() != "[CODE_BLOCK_PLACEHOLDER]":
                    # Replace placeholders and render
                    parts = remaining_content.split("[CODE_BLOCK_PLACEHOLDER]")

                    for i, part in enumerate(parts):
                        if part.strip():
                            markdown = Markdown(part)
                            self.console.print(markdown)

                        # Display code block if there's one after this part
                        if i < len(code_blocks):
                            code, language = code_blocks[i]
                            formatted_code = format_code_block(code, language)
                            self.console.print(formatted_code)
                else:
                    # Only code blocks, display them
                    for code, language in code_blocks:
                        formatted_code = format_code_block(code, language)
                        self.console.print(formatted_code)

            except Exception:
                # Fallback to plain text if markdown fails
                self.console.print(content)
        else:
            # No code blocks, render as markdown
            try:
                markdown = Markdown(content)
                self.console.print(markdown)
            except Exception:
                # Fallback to plain text if markdown fails
                self.console.print(content)

        self.console.print()

    def _handle_offline_response(self, user_input: str):
        """Handle user input in offline mode"""
        # Provide helpful offline responses
        offline_responses = {
            "hello": "Hello! I'm here to help you with coding questions and general assistance.",
            "help": "I can help you with:\n• Programming questions and code examples\n• Explanations and tutorials\n• General questions and conversations\n• Code syntax highlighting and formatting",
            "test": "This is a demo response! The AI assistant is working correctly.",
            "code": "Here's a sample Python function:\n\n```python\ndef hello_world():\n    print('Hello, World!')\n    return 'Success'\n```\n\nThis demonstrates the code highlighting feature!",
            "python": "Python is a great programming language! Here's a quick example:\n\n```python\n# Simple list comprehension\nnumbers = [x**2 for x in range(10)]\nprint(numbers)\n```",
            "javascript": "JavaScript is versatile! Here's an example:\n\n```javascript\n// Arrow function example\nconst greet = (name) => {\n    return `Hello, ${name}!`;\n};\nconsole.log(greet('World'));\n```"
        }

        # Simple keyword matching for offline responses
        user_lower = user_input.lower()
        response = None

        for keyword, reply in offline_responses.items():
            if keyword in user_lower:
                response = reply
                break

        if not response:
            # Generate more natural conversational responses
            import random
            user_lower = user_input.lower()

            # Handle common greetings naturally
            if any(greeting in user_lower for greeting in ['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening']):
                greetings = [
                    "Hello! How can I help you today?",
                    "Hi there! What can I assist you with?",
                    "Hello! I'm here to help with any questions you have.",
                    "Hi! What would you like to know?"
                ]
                response = random.choice(greetings)

            # Handle name questions
            elif any(phrase in user_lower for phrase in ['what is your name', 'who are you', 'what are you called']):
                name_responses = [
                    "I'm an AI assistant here to help you with coding, questions, and conversations.",
                    "I don't have a specific name, but you can call me Assistant. How can I help you?",
                    "I'm your AI coding assistant. What can I help you with today?"
                ]
                response = random.choice(name_responses)

            # Handle how are you questions
            elif any(phrase in user_lower for phrase in ['how are you', 'how do you do', 'how\'s it going']):
                status_responses = [
                    "I'm doing well, thank you! Ready to help with any questions you have.",
                    "I'm here and ready to assist! What can I help you with?",
                    "All good here! How can I help you today?"
                ]
                response = random.choice(status_responses)

            # Default helpful responses
            else:
                helpful_responses = [
                    f"I'd be happy to help you with '{user_input}'. Could you provide more details about what you're looking for?",
                    f"That's an interesting question about '{user_input}'. What specific aspect would you like to explore?",
                    "I'm here to help with coding questions, explanations, and general assistance. What would you like to know more about?",
                    "I can assist with programming questions, provide code examples, and explain concepts. What specific help do you need?"
                ]
                response = random.choice(helpful_responses)

        # Add to conversation history
        ai_message = Message(role="assistant", content=response)
        self.conversation_history.append(ai_message)

        # Display response
        self._display_ai_response(response)